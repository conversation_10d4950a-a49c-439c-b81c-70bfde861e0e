<?php

namespace Tests\Feature\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Client;
use App\Models\PhoneNumber;
use App\Models\Template;
use App\Models\Message;
use App\Models\WhatsAppMessage;
use App\Services\Meta\WhatsApp\MessageService;
use App\Enums\MessageStatus;
use Illuminate\Support\Facades\Queue;

class SendRealTimeMessageTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Client $client;
    private PhoneNumber $phoneNumber;
    private Template $template;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => '<PERSON> Doe',
            'phone' => '+5511999999999'
        ]);

        $this->phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $this->organization->id,
            'phone_number' => '+5511888888888'
        ]);

        $this->template = Template::factory()->approved()->create([
            'organization_id' => $this->organization->id,
            'name' => 'test_template'
        ]);

        // Create WhatsAppTemplate to make it "published"
        \App\Services\Meta\WhatsApp\Models\WhatsAppTemplate::create([
            'template_id' => $this->template->id,
            'external_id' => 'whatsapp_template_123',
            'status' => 'APPROVED'
        ]);

        $this->actingAs($this->user);
    }

    public function test_can_send_realtime_message_successfully()
    {
        // Arrange
        $whatsappResponse = [
            'messages' => [
                ['id' => 'wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI']
            ],
            'messaging_product' => 'whatsapp',
            'contacts' => [
                ['input' => '+5511999999999', 'wa_id' => '5511999999999']
            ]
        ];

        // Mock MessageService
        $this->app->bind(MessageService::class, function () use ($whatsappResponse) {
            $mock = $this->createMock(MessageService::class);
            $mock->expects($this->once())
                 ->method('send')
                 ->willReturn($whatsappResponse);
            return $mock;
        });

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello {{client.name}}! This is a real-time message.',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'is_direct_message' => true
        ]);

        // Assert
        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Message sent successfully'
                ])
                ->assertJsonStructure([
                    'data' => [
                        'message_id',
                        'whatsapp_message_id',
                        'status',
                        'meta_response'
                    ]
                ]);

        // ✅ CRITICAL: Verify Message was created with campaign_id = null
        $this->assertDatabaseHas('messages', [
            'id' => $response->json('data.message_id'),
            'organization_id' => $this->organization->id,
            'campaign_id' => null, // ✅ Critical assertion
            'template_id' => null,
            'client_id' => $this->client->id,
            'message' => 'Hello {{client.name}}! This is a real-time message.',
            'status' => MessageStatus::is_sending,
            'is_direct_message' => true
        ]);

        // Verify response data
        $this->assertEquals('wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI',
                           $response->json('data.whatsapp_message_id'));
        $this->assertEquals('sent', $response->json('data.status'));
        $this->assertEquals($whatsappResponse, $response->json('data.meta_response'));
    }

    public function test_can_send_realtime_message_with_template()
    {
        // Arrange
        $whatsappResponse = [
            'messages' => [['id' => 'wamid.template123']]
        ];

        // Mock MessageService
        $this->app->bind(MessageService::class, function () use ($whatsappResponse) {
            $mock = $this->createMock(MessageService::class);
            $mock->expects($this->once())
                 ->method('send')
                 ->willReturn($whatsappResponse);
            return $mock;
        });

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Template message content',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'template_id' => $this->template->id,
            'is_direct_message' => false
        ]);

        // Assert
        $response->assertStatus(200);

        $this->assertDatabaseHas('messages', [
            'id' => $response->json('data.message_id'),
            'campaign_id' => null, // ✅ Critical: still null even with template
            'template_id' => $this->template->id,
            'client_id' => $this->client->id,
            'is_direct_message' => false
        ]);
    }

    public function test_validates_required_fields()
    {
        // Test missing text
        $response = $this->postJson('/api/message/send-realtime', [
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['text']);

        // Test missing client_id
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'phone_number_id' => $this->phoneNumber->id
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['client_id']);

        // Test missing phone_number_id
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone_number_id']);
    }

    public function test_validates_text_length()
    {
        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => str_repeat('a', 4097), // Exceeds 4096 character limit
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        // Assert
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['text']);
    }

    public function test_validates_client_exists()
    {
        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => 99999, // Non-existent client
            'phone_number_id' => $this->phoneNumber->id
        ]);

        // Assert
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['client_id']);
    }

    public function test_validates_phone_number_exists()
    {
        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => 99999 // Non-existent phone number
        ]);

        // Assert
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone_number_id']);
    }

    public function test_validates_template_exists_when_provided()
    {
        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'template_id' => 99999 // Non-existent template
        ]);

        // Assert
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['template_id']);
    }

    public function test_validates_client_belongs_to_organization()
    {
        // Arrange
        $differentOrg = Organization::factory()->create();
        $clientFromDifferentOrg = Client::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $clientFromDifferentOrg->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        // Assert
        $response->assertStatus(500)
                ->assertJson([
                    'success' => false,
                    'message' => 'Client does not belong to your organization'
                ]);
    }

    public function test_validates_phone_number_belongs_to_organization()
    {
        // Arrange
        $differentOrg = Organization::factory()->create();
        $phoneNumberFromDifferentOrg = PhoneNumber::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $phoneNumberFromDifferentOrg->id
        ]);

        // Assert
        $response->assertStatus(500)
                ->assertJson([
                    'success' => false,
                    'message' => 'Phone number does not belong to your organization'
                ]);
    }

    public function test_validates_template_belongs_to_organization()
    {
        // Arrange
        $differentOrg = Organization::factory()->create();
        $templateFromDifferentOrg = Template::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'template_id' => $templateFromDifferentOrg->id
        ]);

        // Assert
        $response->assertStatus(500)
                ->assertJson([
                    'success' => false,
                    'message' => 'Template does not belong to your organization'
                ]);
    }

    public function test_requires_authentication()
    {
        // Arrange
        $this->app['auth']->forgetGuards();

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        // Assert
        $response->assertStatus(401);
    }

    public function test_handles_whatsapp_service_failure()
    {
        // Arrange
        $this->mock(MessageService::class)
            ->shouldReceive('send')
            ->once()
            ->andThrow(new \Exception('WhatsApp API Error'));

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        // Assert
        $response->assertStatus(500)
                ->assertJson([
                    'success' => false,
                    'message' => 'WhatsApp API Error'
                ]);
    }

    public function test_defaults_is_direct_message_to_true()
    {
        // Arrange
        $this->mock(MessageService::class)
            ->shouldReceive('send')
            ->once()
            ->andReturn(['messages' => [['id' => 'wamid.test']]]);

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
            // is_direct_message not provided
        ]);

        // Assert
        $response->assertStatus(200);

        $this->assertDatabaseHas('messages', [
            'id' => $response->json('data.message_id'),
            'is_direct_message' => true // Should default to true
        ]);
    }

    public function test_can_set_is_direct_message_to_false()
    {
        // Arrange
        $this->mock(MessageService::class)
            ->shouldReceive('send')
            ->once()
            ->andReturn(['messages' => [['id' => 'wamid.test']]]);

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'is_direct_message' => false
        ]);

        // Assert
        $response->assertStatus(200);

        $this->assertDatabaseHas('messages', [
            'id' => $response->json('data.message_id'),
            'is_direct_message' => false
        ]);
    }

    /**
     * 🔥 CRITICAL TEST: Verify WhatsAppMessage is created automatically
     * This test ensures the MessageService::send() method creates the WhatsAppMessage
     * which is essential for webhook tracking to work properly
     */
    public function test_creates_whatsapp_message_automatically()
    {
        // Arrange
        $whatsappMessageId = 'wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI';
        $whatsappResponse = [
            'messages' => [
                ['id' => $whatsappMessageId]
            ],
            'messaging_product' => 'whatsapp',
            'contacts' => [
                ['input' => '+5511999999999', 'wa_id' => '5511999999999']
            ]
        ];

        // We need to use the real MessageService to test the full integration
        // but we'll mock the actual HTTP call to WhatsApp API
        $this->mock(\App\Services\Meta\WhatsApp\WhatsAppService::class)
            ->shouldReceive('post')
            ->once()
            ->andReturn($whatsappResponse);

        // Act
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Hello {{client.name}}!',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        // Assert
        $response->assertStatus(200);

        $messageId = $response->json('data.message_id');

        // ✅ CRITICAL: Verify Message was created
        $this->assertDatabaseHas('messages', [
            'id' => $messageId,
            'campaign_id' => null,
            'client_id' => $this->client->id
        ]);

        // ✅ CRITICAL: Verify WhatsAppMessage was created automatically
        $this->assertDatabaseHas('whatsapp_messages', [
            'message_id' => $messageId,
            'whatsapp_message_id' => $whatsappMessageId,
            'wa_id' => '5511999999999',
            'input_phone' => '+5511999999999',
            'messaging_product' => 'whatsapp'
        ]);

        // Verify the WhatsAppMessage contains the full JSON response
        $whatsappMessage = WhatsAppMessage::where('message_id', $messageId)->first();
        $this->assertNotNull($whatsappMessage);
        $this->assertEquals($whatsappMessageId, $whatsappMessage->whatsapp_message_id);

        $jsonData = json_decode($whatsappMessage->json, true);
        $this->assertEquals($whatsappResponse, $jsonData);
    }

    /**
     * Test webhook compatibility for real-time messages
     * This ensures webhooks can find and update real-time messages
     */
    public function test_webhook_compatibility_for_realtime_messages()
    {
        // Arrange
        $whatsappMessageId = 'wamid.webhook_test_123';
        $whatsappResponse = [
            'messages' => [['id' => $whatsappMessageId]]
        ];

        $this->mock(\App\Services\Meta\WhatsApp\WhatsAppService::class)
            ->shouldReceive('post')
            ->once()
            ->andReturn($whatsappResponse);

        // Act - Send real-time message
        $response = $this->postJson('/api/message/send-realtime', [
            'text' => 'Webhook test message',
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id
        ]);

        $response->assertStatus(200);
        $messageId = $response->json('data.message_id');

        // Verify WhatsAppMessage was created
        $whatsappMessage = WhatsAppMessage::where('whatsapp_message_id', $whatsappMessageId)->first();
        $this->assertNotNull($whatsappMessage);
        $this->assertEquals($messageId, $whatsappMessage->message_id);

        // ✅ CRITICAL: Test that fetchByExternalWamId can find this message
        $repository = app()->make(\App\Repositories\WhatsAppMessageRepository::class);
        $foundMessage = $repository->fetchByExternalWamId($whatsappMessageId);

        $this->assertNotNull($foundMessage);
        $this->assertEquals($whatsappMessageId, $foundMessage->whatsapp_message_id);
        $this->assertEquals($messageId, $foundMessage->message_id);

        // This proves webhooks will work for real-time messages
        // because they use fetchByExternalWamId to find messages
    }
}
