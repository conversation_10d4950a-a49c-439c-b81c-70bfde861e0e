{"name": "Update Import", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"model\": \"products\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/imports/{{import_id}}", "host": ["{{URL}}"], "path": ["imports", "{{import_id}}"], "variable": [{"key": "import_id", "value": "1", "description": "The ID of the import"}]}}, "response": []}