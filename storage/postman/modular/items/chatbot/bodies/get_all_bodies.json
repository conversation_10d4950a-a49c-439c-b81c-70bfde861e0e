{"name": "Get All Bodies", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/bodies?name=&type=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["bodies"], "query": [{"key": "name", "value": "", "description": "Filter by body name"}, {"key": "type", "value": "", "description": "Filter by body type"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}