{"name": "Create Body", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Welcome Body\",\n  \"type\": \"interactive\",\n  \"text\": \"Welcome to our service! How can we help you today?\",\n  \"format\": \"TEXT\"\n}"}, "url": {"raw": "{{URL}}/bodies", "host": ["{{URL}}"], "path": ["bodies"]}}, "response": []}