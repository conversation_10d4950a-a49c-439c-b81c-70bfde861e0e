{"name": "Generate Messages by Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"template_id\": 1,\n  \"variables\": {\n    \"client_name\": \"{{client.name}}\",\n    \"product_name\": \"{{product.name}}\"\n  }\n}"}, "url": {"raw": "{{URL}}/message/generate-messages/{{campaign_id}}", "host": ["{{URL}}"], "path": ["message", "generate-messages", "{{campaign_id}}"]}}, "response": []}