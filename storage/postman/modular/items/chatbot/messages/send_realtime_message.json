{"name": "Send Realtime Message", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number\": \"+5511999999999\",\n  \"message_text\": \"Hello! This is a realtime message.\",\n  \"template_id\": 1,\n  \"variables\": {\n    \"client_name\": \"<PERSON>\"\n  }\n}"}, "url": {"raw": "{{URL}}/message/send-realtime", "host": ["{{URL}}"], "path": ["message", "send-realtime"]}}, "response": []}