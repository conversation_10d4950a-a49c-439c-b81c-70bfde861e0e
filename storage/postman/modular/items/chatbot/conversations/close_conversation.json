{"name": "Close Conversation", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Conversation completed\",\n  \"closed_by_user_id\": 1\n}"}, "url": {"raw": "{{URL}}/conversations/{{conversation_id}}/close", "host": ["{{URL}}"], "path": ["conversations", "{{conversation_id}}", "close"]}}, "response": []}