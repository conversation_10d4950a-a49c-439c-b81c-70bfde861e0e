["User", "Organization", "Profile", "Permission", "Department", "DepartmentUser", "Client", "Brand", "Product", "ProductHistory", "CustomProduct", "Stock", "StockEntry", "StockExit", "<PERSON><PERSON>", "Shop", "Group", "GroupProduct", "Project", "ProjectProduct", "Budget", "BudgetProduct", "Sale", "<PERSON><PERSON>", "Import", "Log", "Subscription", "Flow", "Step", "StepNavigation", "Component", "<PERSON><PERSON>", "Parameter", "Template", "TemplatePublishing", "Campaign", "CampaignAnalytics", "CampaignPerformanceSnapshot", "CampaignStatusHistory", "CampaignCategoryAssignment", "CampaignTagAssignment", "Message", "MessageDeliveryAttempt", "MessageEngagementEvent", "Interaction", "Conversation", "ExchangedMessage", "PhoneNumber", "Category", "Tag", "Lead", "InteractiveMessage", "ListRow", "ListSection", "WhatsAppMessage", "WhatsAppSyncLog", "WhatsAppWebhookLog", "WhatsAppWebhookEntry", "TelegramBot", "TelegramUser", "TelegramChat", "TelegramMessage", "TelegramFile", "AsaasOrganization", "AsaasClient", "AsaasOrganizationCustomer", "As<PERSON>sSale", "AsaasSubscription", "AsaasLog", "PasswordResetToken"]