["get::/give-my-php-info", "get::/give-my-php-v", "post::/login", "post::/register", "post::/auth/password/forgot", "post::/auth/password/reset", "post::/auth/password/validate-token", "post::/telegram/receive-message", "post::/telegram/{bot_id}/receive", "get::/whatsapp/webhook", "post::/whatsapp/webhook", "get::/user", "post::/logout", "post::/logout_all_sessions", "delete::/user/delete", "get::/whatsapp/testWhatsAppToken/{phone_number_id}", "get::/users", "post::/users", "get::/users/{id}", "put::/users/{id}", "delete::/users/{id}", "get::/profiles", "post::/profiles", "get::/profiles/{id}", "put::/profiles/{id}", "delete::/profiles/{id}", "get::/permissions", "post::/permissions", "get::/permissions/{id}", "put::/permissions/{id}", "delete::/permissions/{id}", "get::/organizations", "post::/organizations", "get::/organizations/{id}", "put::/organizations/{id}", "delete::/organizations/{id}", "get::/brands", "post::/brands", "get::/brands/{id}", "put::/brands/{id}", "delete::/brands/{id}", "get::/products", "post::/products", "get::/products/{id}", "put::/products/{id}", "delete::/products/{id}", "get::/clients", "post::/clients", "get::/clients/{id}", "put::/clients/{id}", "delete::/clients/{id}", "get::/projects", "post::/projects", "get::/projects/{id}", "put::/projects/{id}", "delete::/projects/{id}", "get::/budgets", "post::/budgets", "get::/budgets/{id}", "put::/budgets/{id}", "delete::/budgets/{id}", "get::/batches", "post::/batches", "get::/batches/{id}", "put::/batches/{id}", "delete::/batches/{id}", "get::/stock_entries", "post::/stock_entries", "get::/stock_entries/{id}", "put::/stock_entries/{id}", "delete::/stock_entries/{id}", "get::/stock_exits", "post::/stock_exits", "get::/stock_exits/{id}", "put::/stock_exits/{id}", "delete::/stock_exits/{id}", "get::/stocks", "post::/stocks", "get::/stocks/{id}", "put::/stocks/{id}", "delete::/stocks/{id}", "get::/groups", "post::/groups", "get::/groups/{id}", "put::/groups/{id}", "delete::/groups/{id}", "get::/shops", "post::/shops", "get::/shops/{id}", "put::/shops/{id}", "delete::/shops/{id}", "get::/sales", "post::/sales", "get::/sales/{id}", "put::/sales/{id}", "delete::/sales/{id}", "get::/items", "post::/items", "get::/items/{id}", "put::/items/{id}", "delete::/items/{id}", "get::/departments", "post::/departments", "get::/departments/{id}", "put::/departments/{id}", "delete::/departments/{id}", "get::/department_users", "post::/department_users", "get::/department_users/{id}", "put::/department_users/{id}", "delete::/department_users/{id}", "get::/groups_products", "post::/groups_products", "get::/groups_products/{id}", "put::/groups_products/{id}", "delete::/groups_products/{id}", "get::/budgets_products", "post::/budgets_products", "get::/budgets_products/{id}", "put::/budgets_products/{id}", "delete::/budgets_products/{id}", "get::/projects_products", "post::/projects_products", "get::/projects_products/{id}", "put::/projects_products/{id}", "delete::/projects_products/{id}", "get::/products_histories", "post::/products_histories", "get::/products_histories/{id}", "put::/products_histories/{id}", "delete::/products_histories/{id}", "get::/custom_products", "post::/custom_products", "get::/custom_products/{id}", "put::/custom_products/{id}", "delete::/custom_products/{id}", "get::/telegram_users", "post::/telegram_users", "get::/telegram_users/{id}", "put::/telegram_users/{id}", "delete::/telegram_users/{id}", "get::/telegram_bots", "post::/telegram_bots", "get::/telegram_bots/{id}", "put::/telegram_bots/{id}", "delete::/telegram_bots/{id}", "get::/telegram_chats", "post::/telegram_chats", "get::/telegram_chats/{id}", "put::/telegram_chats/{id}", "delete::/telegram_chats/{id}", "get::/telegram_messages", "post::/telegram_messages", "get::/telegram_messages/{id}", "put::/telegram_messages/{id}", "delete::/telegram_messages/{id}", "get::/flows", "post::/flows", "get::/flows/{id}", "put::/flows/{id}", "delete::/flows/{id}", "get::/steps", "post::/steps", "get::/steps/{id}", "put::/steps/{id}", "delete::/steps/{id}", "get::/bodies", "post::/bodies", "get::/bodies/{id}", "put::/bodies/{id}", "delete::/bodies/{id}", "get::/components", "post::/components", "get::/components/{id}", "put::/components/{id}", "delete::/components/{id}", "get::/buttons", "post::/buttons", "get::/buttons/{id}", "put::/buttons/{id}", "delete::/buttons/{id}", "get::/campaigns", "post::/campaigns", "get::/campaigns/{id}", "put::/campaigns/{id}", "delete::/campaigns/{id}", "get::/messages", "post::/messages", "get::/messages/{id}", "put::/messages/{id}", "delete::/messages/{id}", "get::/interactions", "post::/interactions", "get::/interactions/{id}", "put::/interactions/{id}", "delete::/interactions/{id}", "get::/conversations", "post::/conversations", "get::/conversations/{id}", "put::/conversations/{id}", "delete::/conversations/{id}", "get::/templates", "post::/templates", "get::/templates/{id}", "put::/templates/{id}", "delete::/templates/{id}", "get::/parameters", "post::/parameters", "get::/parameters/{id}", "put::/parameters/{id}", "delete::/parameters/{id}", "get::/phone_numbers", "post::/phone_numbers", "get::/phone_numbers/{id}", "put::/phone_numbers/{id}", "delete::/phone_numbers/{id}", "get::/whatsapp_messages", "post::/whatsapp_messages", "get::/whatsapp_messages/{id}", "put::/whatsapp_messages/{id}", "delete::/whatsapp_messages/{id}", "get::/whatsapp_webhook_entries", "get::/whatsapp_webhook_entries/{id}", "get::/exchanged_messages", "post::/exchanged_messages", "get::/exchanged_messages/{id}", "put::/exchanged_messages/{id}", "delete::/exchanged_messages/{id}", "get::/exchanged_messages/chat-by-client/{client_id}", "get::/steps/{step_id}/to-whatsapp-payload", "post::/flow/save", "put::/conversations/{id}/close", "post::/template/save", "post::/template/publish/whatsapp/{id}", "post::/template/republish/whatsapp/{id}", "get::/template/get-to-whatsapp-payload/{id}", "get::/component/get-to-whatsapp-payload/{id}", "get::/message/get-to-whatsapp-payload/{id}", "post::/message/generate-messages/{campaign_id}", "post::/message/send-realtime", "post::/campaign/add-clients/{id}", "post::/campaign/remove-client/{id}", "post::/campaign/launch/{id}", "get::/campaign/{id}/clients", "get::/categories", "post::/categories", "get::/categories/{id}", "put::/categories/{id}", "delete::/categories/{id}", "get::/tags", "get::/tags/most-used", "get::/tags/suggestions", "post::/campaign/{id}/categories", "post::/campaign/{id}/tags", "post::/campaign/{id}/cancel", "get::/campaign/{id}/status-history", "get::/campaign/{id}/status-timeline", "get::/campaign/{id}/messages", "get::/campaign/{id}/messages/failed", "get::/campaign/{id}/messages/statistics", "post::/campaign/{id}/messages/resend-failed", "post::/message/{id}/resend", "get::/message/{id}/delivery-status", "post::/whatsapp/sync/message/{id}", "post::/whatsapp/sync/campaign/{id}", "get::/whatsapp/sync/logs", "get::/whatsapp/sync/entity-logs", "get::/whatsapp/sync/trends", "get::/whatsapp/sync/status-overview", "post::/whatsapp/sync/trigger-proactive", "get::/analytics/dashboard", "get::/analytics/campaign/{id}", "post::/analytics/campaigns/multiple", "post::/analytics/campaigns/compare", "post::/analytics/engagement/record", "post::/analytics/engagement/bulk", "get::/analytics/message/{id}/engagement", "post::/analytics/trigger-calculation", "get::/debug/message/getMessagesAvailableToSent", "get::/imports", "post::/imports", "get::/imports/{id}", "put::/imports/{id}", "delete::/imports/{id}", "post::/import/{id}/process", "post::/project/budget/{budget_id}", "post::/project/{id}/products/", "post::/budget/{id}/products/", "get::/reports/stock_entries", "get::/reports/stock_exits", "post::/batch/{id}/process-at-stock/", "get::/logs/fetch/{id}", "get::/logs/fetch_from_organization/{organization_id}", "get::/logs/fetch_all", "post::/ocr/image-reader/", "get::/reports/{model}/count", "get::/reports/{model}/sum/{column}", "get::/notifications/unread", "get::/notifications/all", "post::/notifications/read", "post::/notifications/read-one/{$id}", "get::/organization/{id}/check-access", "get::/organization/{id}/check-asaas-integration", "post::/subscriptions", "get::/subscriptions/{id}", "put::/subscriptions/{id}", "get::/subscriptions/organization/{organizationId}", "post::/subscriptions/grant-courtesy", "delete::/subscriptions/revoke-courtesy/{organizationId}", "get::/organization/{id}/get-to-asaas-payload", "get::/asaas/account/my-account", "put::/asaas/account/my-account", "get::/asaas/account/balance", "get::/asaas/account/statistics", "post::/asaas/account/subaccount", "get::/asaas/account/subaccounts", "get::/asaas/account/subaccount", "get::/asaas/account/subaccount/search", "get::/asaas/account/organization/sync", "put::/asaas/account/subaccount", "delete::/asaas/account/subaccount", "delete::/asaas/account/my-account/subaccount", "post::/asaas/customer", "get::/asaas/customers", "get::/asaas/customer/{client_id}", "put::/asaas/customer/{client_id}", "delete::/asaas/customer/{client_id}", "get::/asaas/org-customer/{organization_id}", "put::/asaas/org-customer/{organization_id}", "delete::/asaas/org-customer/{organization_id}", "get::/asaas/customer/search/email", "get::/asaas/customer/search/document", "get::/asaas/customer/{client_id}/notifications", "get::/asaas/org-customer/{organization_id}/notifications", "post::/asaas/payment", "get::/asaas/payments", "get::/asaas/payment/{payment_id}", "put::/asaas/payment/{payment_id}", "delete::/asaas/payment/{payment_id}", "post::/asaas/subscription", "get::/asaas/subscriptions", "get::/asaas/subscription/{subscription_id}", "put::/asaas/subscription/{subscription_id}", "delete::/asaas/subscription/{subscription_id}", "get::/asaas/subscription/{subscription_id}/payments", "get::/asaas-resources/organizations", "post::/asaas-resources/organizations", "get::/asaas-resources/organizations/{id}", "put::/asaas-resources/organizations/{id}", "delete::/asaas-resources/organizations/{id}", "get::/asaas-resources/organization-customers", "post::/asaas-resources/organization-customers", "get::/asaas-resources/organization-customers/{id}", "put::/asaas-resources/organization-customers/{id}", "delete::/asaas-resources/organization-customers/{id}", "get::/asaas-resources/clients", "post::/asaas-resources/clients", "get::/asaas-resources/clients/{id}", "put::/asaas-resources/clients/{id}", "delete::/asaas-resources/clients/{id}", "get::/asaas-resources/sales", "post::/asaas-resources/sales", "get::/asaas-resources/sales/{id}", "put::/asaas-resources/sales/{id}", "delete::/asaas-resources/sales/{id}", "get::/asaas-resources/subscriptions", "post::/asaas-resources/subscriptions", "get::/asaas-resources/subscriptions/{id}", "put::/asaas-resources/subscriptions/{id}", "delete::/asaas-resources/subscriptions/{id}", "get::/whatsapp-webhook-logs", "post::/whatsapp-webhook-logs", "get::/whatsapp-webhook-logs/{id}", "put::/whatsapp-webhook-logs/{id}", "delete::/whatsapp-webhook-logs/{id}", "get::/whatsapp-webhook-logs/recent/list", "get::/whatsapp-webhook-logs/event-type/{eventType}", "get::/whatsapp-webhook-logs/status/{status}", "get::/whatsapp-webhook-logs/statistics/summary", "get::/profile/permissions", "get::/profile/permission/{slug}/can", "get::/user/permissions", "get::/user/permission/{slug}/can", "post::/profile/permission/add", "post::/profile/permission/remove", "get::/profile/{profileId}/permissions", "post::/organization/{organizationId}/create-admin-profile"]