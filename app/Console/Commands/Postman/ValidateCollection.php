<?php

namespace App\Console\Commands\Postman;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ValidateCollection extends Command
{
    protected $signature = 'postman:validate
                            {--summary : Show only the final summary}
                            {--print : Show detailed lists of missing/extra endpoints}
                            {--collection= : Custom collection file path (default: storage/postman/ObvioAPI.postman_collection.json)}';

    protected $description = 'Validate the built Postman collection against reference endpoint definitions';

    private string $docPath;
    private string $collectionPath;

    public function handle(): int
    {
        $this->docPath = storage_path('postman/doc');
        $this->collectionPath = $this->option('collection') ?? storage_path('postman/ObvioAPI.postman_collection.json');

        try {
            // Resolve files
            $entitiesFile = $this->docPath . '/Obvio.entities.json';
            $requestsFile = $this->docPath . '/Obvio.requests.json';
            $apiFile = $this->docPath . '/Obvio.api.json';

            $this->info('🔍 Validando Collection do Postman...');

            $files = [
                'Entities' => $entitiesFile,
                'Requests' => $requestsFile,
                'API Structure' => $apiFile,
                'Collection' => $this->collectionPath,
            ];

            foreach ($files as $name => $file) {
                if (!File::exists($file)) {
                    $this->error("❌ Arquivo não encontrado: $name ($file)");
                    return Command::FAILURE;
                }
                if (!$this->option('summary')) {
                    $this->line("✅ $name: " . basename($file));
                }
            }

            $entities = json_decode(File::get($entitiesFile), true);
            $requests = json_decode(File::get($requestsFile), true);
            $apiStructure = json_decode(File::get($apiFile), true);
            $collection = json_decode(File::get($this->collectionPath), true);

            // Extract collection endpoints
            $collectionEndpoints = $this->extractEndpointsFromCollection($collection['item'] ?? []);

            // Stats
            if (!$this->option('summary')) {
                $this->newLine();
                $this->line('📊 Estatísticas:');
                $this->line('  • Entidades de referência: ' . count($entities));
                $this->line('  • Endpoints de referência: ' . count($requests));
                $this->line('  • Endpoints na collection: ' . count($collectionEndpoints));
                $this->newLine();
            }

            // Pattern-aware matching
            [$missingInCollection, $extraInCollection] = $this->compareEndpointsPatternAware($requests, $collectionEndpoints);

            // Missing report
            if (!empty($missingInCollection)) {
                $this->error('❌ Endpoints faltantes na collection (' . count($missingInCollection) . '):');
                if ($this->option('print') || !$this->option('summary')) {
                    foreach (array_slice($missingInCollection, 0, 20) as $ep) {
                        $this->line('  - ' . $ep);
                    }
                    if (count($missingInCollection) > 20) {
                        $this->line('  ... e mais ' . (count($missingInCollection) - 20) . ' endpoints');
                    }
                }
                if (!$this->option('summary')) $this->newLine();
            } else {
                if (!$this->option('summary')) $this->info('✅ Todos os endpoints de referência estão na collection!');
                if (!$this->option('summary')) $this->newLine();
            }

            // Extra report
            if (!empty($extraInCollection)) {
                if (!$this->option('summary')) $this->warn('⚠️  Endpoints extras na collection (' . count($extraInCollection) . '):');
                if ($this->option('print')) {
                    foreach ($extraInCollection as $ep) {
                        $this->line('  - ' . $ep);
                    }
                } elseif (!$this->option('summary')) {
                    foreach (array_slice($extraInCollection, 0, 10) as $ep) {
                        $this->line('  - ' . $ep);
                    }
                    if (count($extraInCollection) > 10) {
                        $this->line('  ... e mais ' . (count($extraInCollection) - 10) . ' endpoints');
                    }
                }
                if (!$this->option('summary')) $this->newLine();
            } else {
                if (!$this->option('summary')) $this->info('✅ Nenhum endpoint extra encontrado na collection!');
                if (!$this->option('summary')) $this->newLine();
            }

            // Module analysis (coverage)
            if (!$this->option('summary')) $this->line('📋 Análise por módulo:');
            foreach ($apiStructure as $module => $entitiesDef) {
                $moduleEndpoints = [];
                $this->extractEndpointsFromStructure($entitiesDef, $moduleEndpoints);

                $matchedCount = $this->countMatchedReferences($moduleEndpoints, $collectionEndpoints);
                $coverage = count($moduleEndpoints) > 0 ? round(($matchedCount / count($moduleEndpoints)) * 100, 1) : 0;

                if (!$this->option('summary')) {
                    $this->line("  • $module: $coverage% ($matchedCount/" . count($moduleEndpoints) . ' )');
                }
            }

            // Final summary
            $totalMatched = $this->countMatchedReferences($requests, $collectionEndpoints);
            $totalCoverage = count($requests) > 0 ? round(($totalMatched / count($requests)) * 100, 1) : 0;
            $missingCount = count($missingInCollection);
            $extraCount = count($extraInCollection);

            if (!$this->option('summary')) $this->newLine();
            $this->line('🎯 Resumo Final:');
            $this->line('  • Cobertura total: ' . $totalCoverage . '%');
            $this->line('  • Endpoints faltantes: ' . $missingCount);
            $this->line('  • Endpoints extras: ' . $extraCount);

            if ($missingCount == 0 && $extraCount == 0) {
                $this->line('');
                $this->info('🎉 Collection está 100% sincronizada com a referência!');
            } elseif ($missingCount == 0) {
                $this->line('');
                $this->info('✅ Todos os endpoints estão cobertos, mas há endpoints extras na collection.');
            } else {
                $this->line('');
                $this->warn('⚠️  Collection precisa ser atualizada para incluir endpoints faltantes.');
            }

            if (!$this->option('summary')) {
                $this->newLine();
                $this->line('📝 Para atualizar a collection, execute:');
                $this->line('  php artisan postman:build');
                $this->newLine();
            }

            return Command::SUCCESS;
        } catch (\Throwable $e) {
            $this->error('Erro: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function extractEndpointsFromCollection(array $items, array &$endpoints = []): array
    {
        foreach ($items as $item) {
            if (isset($item['request'])) {
                $method = strtolower($item['request']['method'] ?? '');
                $url = $item['request']['url'] ?? '';

                // Extract path
                if (is_array($url)) {
                    $path = '/' . implode('/', $url['path'] ?? []);
                } else {
                    $parsed = parse_url((string)$url);
                    $path = $parsed['path'] ?? '';
                }

                $endpoints[] = $method . '::' . $path;
            } elseif (isset($item['item'])) {
                $this->extractEndpointsFromCollection($item['item'], $endpoints);
            }
        }
        return $endpoints;
    }

    private function extractEndpointsFromStructure($data, array &$endpoints = []): array
    {
        foreach ($data as $value) {
            if (is_array($value)) {
                if (isset($value[0]) && is_string($value[0]) && strpos($value[0], '::') !== false) {
                    $endpoints = array_merge($endpoints, $value);
                } else {
                    $this->extractEndpointsFromStructure($value, $endpoints);
                }
            }
        }
        return $endpoints;
    }

    private function referenceToRegex(string $reference): string
    {
        // escape regex delimiters, then replace {var} with a path segment matcher
        [$method, $path] = explode('::', $reference, 2);
        $method = preg_quote($method, '/');
        // Replace {var} patterns with a segment wildcard
        $pattern = preg_replace('/\{[^}]+\}/', '[^/]+', $path);
        // Ensure starts with /
        if ($pattern === '' || $pattern[0] !== '/') {
            $pattern = '/' . $pattern;
        }
        $pattern = str_replace('/', '\/', $pattern);
        return '/^' . $method . '::' . $pattern . '$/';
    }

    /**
     * Compare reference endpoints (with {vars}) against concrete collection endpoints.
     * Returns [missingReferences, extraCollection]
     */
    private function compareEndpointsPatternAware(array $references, array $collectionEndpoints): array
    {
        // Build regex map for references
        $regexes = array_map(fn($ref) => $this->referenceToRegex($ref), $references);

        // Determine which references are matched by any collection endpoint
        $matchedRefs = array_fill(0, count($references), false);
        foreach ($collectionEndpoints as $candidate) {
            foreach ($regexes as $i => $regex) {
                if (!$matchedRefs[$i] && preg_match($regex, $candidate)) {
                    $matchedRefs[$i] = true;
                }
            }
        }

        $missing = [];
        foreach ($references as $i => $ref) {
            if (!$matchedRefs[$i]) {
                $missing[] = $ref;
            }
        }

        // Extra endpoints: collection endpoints that do not match ANY reference regex
        $extra = [];
        foreach ($collectionEndpoints as $candidate) {
            $matched = false;
            foreach ($regexes as $regex) {
                if (preg_match($regex, $candidate)) {
                    $matched = true;
                    break;
                }
            }
            if (!$matched) {
                $extra[] = $candidate;
            }
        }

        return [$missing, $extra];
    }

    private function countMatchedReferences(array $references, array $collectionEndpoints): int
    {
        $regexes = array_map(fn($ref) => $this->referenceToRegex($ref), $references);
        $matchedRefs = array_fill(0, count($references), false);
        foreach ($collectionEndpoints as $candidate) {
            foreach ($regexes as $i => $regex) {
                if (!$matchedRefs[$i] && preg_match($regex, $candidate)) {
                    $matchedRefs[$i] = true;
                }
            }
        }
        return collect($matchedRefs)->filter()->count();
    }
}
