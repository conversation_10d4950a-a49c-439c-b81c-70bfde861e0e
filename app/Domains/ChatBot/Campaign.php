<?php

namespace App\Domains\ChatBot;

use App\Domains\Inventory\Client;
use App\Enums\CampaignStatus;
use Carbon\Carbon;

class Campaign
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $template_id;
    public ?int $phone_number_id;

    public ?string $name;
    public ?string $description;

    public ?bool $is_scheduled;
    public ?bool $is_sent;
    public ?bool $is_sending;
    public ?bool $is_direct_message;
    public ?int $message_count;
    public ?CampaignStatus $status;

    public ?Carbon $sent_at;
    public ?Carbon $scheduled_at;
    public ?Carbon $cancelled_at;
    public ?Carbon $failed_at;

    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public ?Template $template;
    public ?PhoneNumber $phone_number;

    /** @var Message[]|null $messages */
    public ?array $messages;

    /** @var Client[]|null $clients */
    public ?array $clients;

    /** @var Parameter[]|null $parameters */
    public ?array $parameters;

    /** @var Category[]|null $categories */
    public ?array $categories;

    /** @var Tag[]|null $tags */
    public ?array $tags;

    /** @var CampaignStatusHistory[]|null $status_history */
    public ?array $status_history;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?int $template_id,
        ?int $phone_number_id = null,
        ?string $name = null,
        ?string $description = null,
        ?bool $is_scheduled = false,
        ?bool $is_sent = false,
        ?bool $is_sending = false,
        ?bool $is_direct_message = false,
        ?int $message_count = 0,
        ?CampaignStatus $status = null,
        ?Carbon $sent_at = null,
        ?Carbon $scheduled_at = null,
        ?Carbon $cancelled_at = null,
        ?Carbon $failed_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Template $template = null,
        ?PhoneNumber $phone_number = null,
        ?array $messages = null,
        ?array $clients = null,
        ?array $parameters = null,
        ?array $categories = null,
        ?array $tags = null,
        ?array $status_history = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->template_id = $template_id;
        $this->phone_number_id = $phone_number_id;
        $this->name = $name;
        $this->description = $description;
        $this->is_scheduled = $is_scheduled;
        $this->is_sent = $is_sent;
        $this->is_sending = $is_sending;
        $this->is_direct_message = $is_direct_message;
        $this->message_count = $message_count;
        $this->status = $status ?? CampaignStatus::DRAFT;
        $this->sent_at = $sent_at;
        $this->scheduled_at = $scheduled_at;
        $this->cancelled_at = $cancelled_at;
        $this->failed_at = $failed_at;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->template = $template;
        $this->phone_number = $phone_number;
        $this->messages = $messages;
        $this->clients = $clients;
        $this->parameters = $parameters;
        $this->categories = $categories;
        $this->tags = $tags;
        $this->status_history = $status_history;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "template_id" => $this->template_id,
            "phone_number_id" => $this->phone_number_id,
            "name" => $this->name,
            "description" => $this->description,
            "is_scheduled" => $this->is_scheduled,
            "is_sent" => $this->is_sent,
            "is_sending" => $this->is_sending,
            "is_direct_message" => $this->is_direct_message,
            "message_count" => $this->message_count,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "scheduled_at" => $this->scheduled_at?->format("Y-m-d H:i:s"),
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "template" => $this->template?->toArray(),
            "phone_number" => $this->phone_number?->toArray(),
            "messages" => $this->messages,
            "clients" => $this->clients,
            "parameters" => $this->parameters,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "template_id" => $this->template_id,
            "phone_number_id" => $this->phone_number_id,
            "name" => $this->name,
            "description" => $this->description,
            "is_scheduled" => $this->is_scheduled,
            "is_sent" => $this->is_sent,
            "is_sending" => $this->is_sending,
            "is_direct_message" => $this->is_direct_message,
            "message_count" => $this->message_count,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "scheduled_at" => $this->scheduled_at?->format("Y-m-d H:i:s"),
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "template_id" => $this->template_id,
            "phone_number_id" => $this->phone_number_id,
            "name" => $this->name,
            "description" => $this->description,
            "is_scheduled" => $this->is_scheduled,
            "is_sent" => $this->is_sent,
            "is_sending" => $this->is_sending,
            "is_direct_message" => $this->is_direct_message,
            "message_count" => $this->message_count,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "scheduled_at" => $this->scheduled_at?->format("Y-m-d H:i:s"),
        ];
    }

    /**
     * Get category IDs assigned to this campaign
     */
    public function getCategoryIds(): array
    {
        if (!$this->categories) {
            return [];
        }
        return array_map(fn($category) => $category->id, $this->categories);
    }

    /**
     * Get tag IDs assigned to this campaign
     */
    public function getTagIds(): array
    {
        if (!$this->tags) {
            return [];
        }
        return array_map(fn($tag) => $tag->id, $this->tags);
    }

    /**
     * Check if campaign has specific category
     */
    public function hasCategory(int $categoryId): bool
    {
        return in_array($categoryId, $this->getCategoryIds());
    }

    /**
     * Check if campaign has specific tag
     */
    public function hasTag(int $tagId): bool
    {
        return in_array($tagId, $this->getTagIds());
    }

    /**
     * Get current status (with fallback to boolean fields)
     */
    public function getCurrentStatus(): CampaignStatus
    {
        if ($this->status) {
            return $this->status;
        }

        // Fallback to boolean fields for backward compatibility
        return CampaignStatus::fromBooleans(
            $this->is_sent ?? false,
            $this->is_sending ?? false,
            $this->is_scheduled ?? false,
            false, // We don't have message data in domain
            $this->cancelled_at !== null
        );
    }

    /**
     * Check if campaign can be edited
     */
    public function canEdit(): bool
    {
        return $this->getCurrentStatus()->canEdit();
    }

    /**
     * Check if campaign can be cancelled
     */
    public function canCancel(): bool
    {
        return $this->getCurrentStatus()->canCancel();
    }

    /**
     * Check if campaign can be launched
     */
    public function canLaunch(): bool
    {
        return $this->getCurrentStatus()->canLaunch();
    }

    /**
     * Check if campaign is in progress
     */
    public function isInProgress(): bool
    {
        return $this->getCurrentStatus()->isInProgress();
    }

    /**
     * Check if campaign is finished
     */
    public function isFinished(): bool
    {
        return $this->getCurrentStatus()->isFinished();
    }

    /**
     * Update status and sync boolean fields
     */
    public function updateStatus(CampaignStatus $newStatus): void
    {
        $this->status = $newStatus;

        // Update boolean fields for backward compatibility
        $booleans = $newStatus->toBooleans();
        $this->is_sent = $booleans['is_sent'];
        $this->is_sending = $booleans['is_sending'];
        $this->is_scheduled = $booleans['is_scheduled'];

        // Update timestamp fields
        switch ($newStatus) {
            case CampaignStatus::CANCELLED:
                $this->cancelled_at = now();
                break;
            case CampaignStatus::FAILED:
                $this->failed_at = now();
                break;
            case CampaignStatus::COMPLETED:
                $this->sent_at = now();
                break;
        }
    }
}
