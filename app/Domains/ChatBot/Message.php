<?php

namespace App\Domains\ChatBot;
use App\Domains\Inventory\Client;
use App\Enums\MessageStatus;
use App\Services\VariableSubstitution\VariableSubstitutionService;
use Carbon\Carbon;

class Message
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $campaign_id;
    public ?int $conversation_id;
    public ?int $step_id;
    public ?int $template_id;
    public ?int $client_id;

    public ?string $message;

    public ?MessageStatus $status;

    public ?bool $is_sent;
    public ?bool $is_delivered;
    public ?bool $is_fail;
    public ?bool $is_read;
    public ?bool $is_direct_message;
    public ?bool $is_from_chatbot;
    public ?bool $is_from_campaign;

    // Delivery tracking fields
    public ?int $delivery_attempts;
    public ?Carbon $last_attempt_at;
    public ?int $max_retries;
    public ?Carbon $next_retry_at;
    public ?string $last_error_message;

    public ?Carbon $sent_at;
    public ?Carbon $scheduled_at;

    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public ?Client $client;
    public ?Template $template;
    public ?Campaign $campaign;
    public ?Conversation $conversation;
    public ?Step $step;

    /** @var MessageDeliveryAttempt[]|null $delivery_attempts_history */
    public ?array $delivery_attempts_history;

    public mixed $component_domain = null;

    /** @var bool $should_render_components // USED ONLY INTERNALLY // */
    private bool $should_render_components = false;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $campaign_id,
        ?int $conversation_id,
        ?int $step_id,
        ?int $template_id,
        ?int $client_id,
        ?string $message,
        ?MessageStatus $status = MessageStatus::is_draft,
        ?bool $is_sent = false,
        ?bool $is_delivered = false,
        ?bool $is_fail = false,
        ?bool $is_read = false,
        ?bool $is_direct_message = false,
        ?bool $is_from_chatbot = false,
        ?bool $is_from_campaign = true,
        ?int $delivery_attempts = 0,
        ?Carbon $last_attempt_at = null,
        ?int $max_retries = 3,
        ?Carbon $next_retry_at = null,
        ?string $last_error_message = null,
        ?Carbon $sent_at = null,
        ?Carbon $scheduled_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Client $client = null,
        ?Template $template = null,
        ?Campaign $campaign = null,
        ?Conversation $conversation = null,
        ?Step $step = null,
        ?array $delivery_attempts_history = null,
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->campaign_id = $campaign_id;
        $this->conversation_id = $conversation_id;
        $this->step_id = $step_id;
        $this->template_id = $template_id;
        $this->client_id = $client_id;
        $this->message = $message;
        $this->status = $status;
        $this->is_sent = $is_sent;
        $this->is_delivered = $is_delivered;
        $this->is_fail = $is_fail;
        $this->is_read = $is_read;
        $this->is_direct_message = $is_direct_message;
        $this->is_from_chatbot = $is_from_chatbot;
        $this->is_from_campaign = $is_from_campaign;
        $this->delivery_attempts = $delivery_attempts;
        $this->last_attempt_at = $last_attempt_at;
        $this->max_retries = $max_retries;
        $this->next_retry_at = $next_retry_at;
        $this->last_error_message = $last_error_message;
        $this->sent_at = $sent_at;
        $this->scheduled_at = $scheduled_at;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->client = $client;
        $this->template = $template;
        $this->campaign = $campaign;
        $this->conversation = $conversation;
        $this->step = $step;
        $this->delivery_attempts_history = $delivery_attempts_history;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "campaign_id" => $this->campaign_id,
            "conversation_id" => $this->conversation_id,
            "step_id" => $this->step_id,
            "template_id" => $this->template_id,
            "client_id" => $this->client_id,
            "message" => $this->message,
            "status" => $this->status,
            "is_sent" => $this->is_sent,
            "is_delivered" => $this->is_delivered,
            "is_fail" => $this->is_fail,
            "is_read" => $this->is_read,
            "is_direct_message" => $this->is_direct_message,
            "is_from_chatbot" => $this->is_from_chatbot,
            "is_from_campaign" => $this->is_from_campaign,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "scheduled_at" => $this->scheduled_at?->format("Y-m-d H:i:s"),
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "client" => $this->client?->toArray(),
            "template" => $this->template?->toArray(),
            "campaign" => $this->campaign?->toArray(),
            "conversation" => $this->conversation?->toArray(),
            "step" => $this->step?->toArray(),
        ];
    }
    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "campaign_id" => $this->campaign_id,
            "conversation_id" => $this->conversation_id,
            "step_id" => $this->step_id,
            "template_id" => $this->template_id,
            "client_id" => $this->client_id,
            "message" => $this->message,
            "status" => $this->status,
            "is_sent" => $this->is_sent,
            "is_delivered" => $this->is_delivered,
            "is_fail" => $this->is_fail,
            "is_read" => $this->is_read,
            "is_direct_message" => $this->is_direct_message,
            "is_from_chatbot" => $this->is_from_chatbot,
            "is_from_campaign" => $this->is_from_campaign,
            "delivery_attempts" => $this->delivery_attempts,
            "last_attempt_at" => $this->last_attempt_at?->format("Y-m-d H:i:s"),
            "max_retries" => $this->max_retries,
            "next_retry_at" => $this->next_retry_at?->format("Y-m-d H:i:s"),
            "last_error_message" => $this->last_error_message,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "scheduled_at" => $this->scheduled_at?->format("Y-m-d H:i:s"),
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "template_id" => $this->template_id,
            "message" => $this->message,
            "status" => $this->status,
            "is_sent" => $this->is_sent,
            "is_delivered" => $this->is_delivered,
            "is_fail" => $this->is_fail,
            "is_read" => $this->is_read,
            "is_direct_message" => $this->is_direct_message,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "scheduled_at" => $this->scheduled_at?->format("Y-m-d H:i:s"),
        ];
    }

    public function send() : void {
        $this->is_sent = true;
        $this->is_fail = false;
        $this->sent_at = Carbon::now();
        $this->status = MessageStatus::is_sent;
    }

    public function fail() : void {
        $this->is_sent = false;
        $this->is_delivered = false;
        $this->is_fail = true;
        $this->sent_at = Carbon::now();
        $this->status = MessageStatus::is_failed;
    }

    public function deliver() : void {
        $this->is_delivered = true;
        $this->is_fail = false;
        $this->status = MessageStatus::is_delivered;
    }

    public function markAsRead() : void {
        $this->is_read = true;
        $this->is_delivered = true;
        $this->is_fail = false;
        $this->status = MessageStatus::is_read;
    }

    /**
     * Atualiza o status da mensagem baseado no status do WhatsApp
     */
    public function updateMessageStatus(string $whatsAppStatus): void
    {
        switch ($whatsAppStatus) {
            case 'sent':
                $this->send();
                break;
            case 'delivered':
                $this->deliver();
                break;
            case 'read':
                $this->markAsRead();
                break;
            case 'failed':
                $this->fail();
                break;
        }
    }

    public function launch(bool $is_direct_message = false) : void {
        $this->status = MessageStatus::is_sending;
        $this->is_sent = false;
        $this->is_fail = false;
        $this->sent_at = null;
        $this->scheduled_at = $this->campaign?->scheduled_at ?? null;
        $this->is_direct_message = $is_direct_message;
    }

    /** @return Component[] */
    public function components() : array {
        return $this->template->components ?? [];
    }

    /** @return Parameter[] */
    public function parameters() : array {
        $parameters = [];
        foreach($this->components() as $component){
            foreach($component->parameters as $parameter){
                $parameters[] = $parameter->toWhatsAppPayload(
                    $this->getComponentDomain()
                );
            }
        }
        return $parameters;
    }

    public function getComponentDomain() : mixed {
        return $this->component_domain ?? $this->client;
    }

    /**
     * Get processed message with variable substitution
     *
     * @return string
     */
    public function getProcessedMessage(): string
    {
        if (!$this->message) {
            return '';
        }

        $substitutionService = new VariableSubstitutionService();

        $availableModels = $this->getAvailableModelsForSubstitution();

        return $substitutionService->substitute($this->message, $availableModels);
    }

    /**
     * Get available models for variable substitution
     *
     * @return array
     */
    private function getAvailableModelsForSubstitution(): array
    {
        $models = [];

        if ($this->client) {
            $models['client'] = $this->client;
        }

        if ($this->campaign) {
            $models['campaign'] = $this->campaign;
        }

        if ($this->template) {
            $models['template'] = $this->template;
        }

        // Add phone number if available through template or campaign
        if ($this->template && $this->template->phone_number) {
            $models['phone_number'] = $this->template->phone_number;
        } elseif ($this->campaign && $this->campaign->phone_number) {
            $models['phone_number'] = $this->campaign->phone_number;
        }

        // Add organization if available through client
        if ($this->client && property_exists($this->client, 'organization_id')) {
            // TODO: Load organization if needed
            // $models['organization'] = $organizationRepository->fetchById($this->client->organization_id);
        }

        return $models;
    }

    /**
     * Generate WhatsApp payload - Fixed to handle both template and text messages
     *
     * @return array
     */
    public function toWhatsAppPayload(): array
    {
        if (!$this->client || !$this->client->phone) {
            throw new \RuntimeException("Missing client phone");
        }
        if ($this->template) {
            return $this->toWhatsAppTemplatePayload();
        }
        return $this->toWhatsAppTextPayload();
    }

    /**
     * Generate WhatsApp template message payload
     *
     * @return array
     */
    public function toWhatsAppTemplatePayload(): array
    {
        if (!$this->template) {
            throw new \RuntimeException("Template is required for template message");
        }

        if ($this->is_direct_message) {
            return $this->toWhatsAppDirectPayload();
        }

        $components = $this->renderComponentsToTemplateWhatsAppPayload();
        $templatePayload = [
            'name' => $this->template->name,
            'language' => [ 'code' => $this->template->language ?? 'pt_BR' ],
        ];
        if ($this->should_render_components){
            $templatePayload['components'] = $components;
        }
        return [
            'messaging_product' => 'whatsapp',
            'to' => $this->client->internationalPhone(),
            'type' => 'template',
            'template' => $templatePayload,
        ];
    }

    /**
     * Generate WhatsApp direct message payload (interactive format)
     *
     * @return array
     */
    public function toWhatsAppDirectPayload(): array
    {
        if (!$this->client || !$this->client->phone) {
            throw new \RuntimeException("Missing client phone");
        }

        $availableModels = $this->getAvailableModelsForSubstitution();

        $header = null;
        $body = null;
        $footer = null;
        $buttons = [];

        foreach ($this->components() as $component) {
            switch (strtolower($component->type)) {
                case 'header':
                    $header = $this->processComponentText($component, $availableModels);
                    break;
                case 'body':
                    $body = $this->processComponentText($component, $availableModels);
                    break;
                case 'footer':
                    $footer = $this->processComponentText($component, $availableModels);
                    break;
                case 'buttons':
                    $buttons = $this->processButtons($component, $availableModels);
                    break;
            }
        }

        if (empty($body)) {
            throw new \RuntimeException("Body is required for direct message");
        }
        if (empty($buttons)) {
            return $this->toWhatsAppForcedTextPayload($header, $body, $footer);
        }

        $interactive = [
            'type' => 'button',
            'body' => [
                'text' => $body
            ]
        ];
        if ($header) {
            $interactive['header'] = [
                'type' => 'text',
                'text' => $header
            ];
        }
        if ($footer) {
            $interactive['footer'] = [
                'text' => $footer
            ];
        }
        if (!empty($buttons)) {
            $interactive['action'] = [
                'buttons' => $buttons
            ];
        }

        return [
            'messaging_product' => 'whatsapp',
            'to' => $this->client->internationalPhone(),
            'type' => 'interactive',
            'interactive' => $interactive
        ];
    }

    public function toWhatsAppForcedTextPayload(?string $header, string $body, ?string $footer): array
    {
        if($header) {
            $body = "*".$header."*" . "\n\n" . $body;
        }
        if($footer) {
            $body = $body . "\n\n" . "*".$footer."*";
        }
        return [
            'messaging_product' => 'whatsapp',
            'to' => $this->client->internationalPhone(),
            'type' => 'text',
            'text' => [
                'body' => $body
            ],
        ];
    }

    private function renderComponentsToTemplateWhatsAppPayload(): array
    {
        $this->should_render_components = false;
        $availableModels = $this->getAvailableModelsForSubstitution();
        $components = [];
        foreach ($this->components() as $component) {
            if($component->type !== "body" && $component->type !== "BODY"){
                continue;
            }
            $componentPayload = $component->toWhatsAppMessagePayload($availableModels);
            if($componentPayload){
                $this->should_render_components = true;
                $components[] = $componentPayload;
            }
        }
        return $components;
    }

    /**
     * Generate WhatsApp text message payload
     *
     * @return array
     */
    public function toWhatsAppTextPayload(): array
    {
        $processedMessage = $this->getProcessedMessage();

        if (empty($processedMessage)) {
            throw new \RuntimeException("Message content is empty");
        }

        return [
            'messaging_product' => 'whatsapp',
            'to' => $this->client->internationalPhone(),
            'type' => 'text',
            'text' => [
                'body' => $processedMessage
            ],
        ];
    }

    /**
     * Process component text with variable substitution
     *
     * @param Component $component
     * @param array $availableModels
     * @return string|null
     */
    private function processComponentText($component, array $availableModels): ?string
    {
        if (!$component->text) {
            return null;
        }

        $substitutionService = new VariableSubstitutionService();
        return $substitutionService->substitute($component->text, $availableModels);
    }

    /**
     * Process buttons from component
     *
     * @param Component $component
     * @param array $availableModels
     * @return array
     */
    private function processButtons($component, array $availableModels): array
    {
        $buttons = [];

        if (!$component->buttons) {
            return $buttons;
        }

        foreach ($component->buttons as $button) {
            if (!$button->text) {
                continue;
            }

            $substitutionService = new VariableSubstitutionService();
            $processedText = $substitutionService->substitute($button->text, $availableModels);

            $buttons[] = [
                'type' => 'reply',
                'reply' => [
                    'id' => $button->id ?? uniqid(),
                    'title' => $processedText
                ]
            ];
        }

        return $buttons;
    }

    /**
     * Check if message can be retried
     */
    public function canRetry(): bool
    {
        return ($this->delivery_attempts ?? 0) < ($this->max_retries ?? 3) &&
               $this->status === MessageStatus::is_failed;
    }

    /**
     * Check if message is ready for retry
     */
    public function isReadyForRetry(): bool
    {
        return $this->canRetry() &&
               ($this->next_retry_at === null || $this->next_retry_at <= now());
    }

    /**
     * Calculate next retry time using exponential backoff
     */
    public function calculateNextRetryTime(): ?Carbon
    {
        if (!$this->canRetry()) {
            return null;
        }

        // Exponential backoff: 1min, 5min, 15min, 30min, 1hour
        $backoffMinutes = match($this->delivery_attempts ?? 0) {
            0 => 1,
            1 => 5,
            2 => 15,
            3 => 30,
            default => 60
        };

        return now()->addMinutes($backoffMinutes);
    }

    /**
     * Record a delivery attempt
     */
    public function recordDeliveryAttempt(MessageStatus $status, ?string $errorMessage = null): void
    {
        $this->delivery_attempts = ($this->delivery_attempts ?? 0) + 1;
        $this->last_attempt_at = now();
        $this->status = $status;

        if ($status === MessageStatus::is_failed) {
            $this->last_error_message = $errorMessage;
            $this->next_retry_at = $this->calculateNextRetryTime();
            $this->is_fail = true;
        } else {
            $this->last_error_message = null;
            $this->next_retry_at = null;
            if ($status === MessageStatus::is_sent) {
                $this->sent_at = now();
                $this->is_sent = true;
                $this->is_fail = false;
            }
        }
    }

    /**
     * Check if message has failed permanently (max retries reached)
     */
    public function hasFailedPermanently(): bool
    {
        return ($this->delivery_attempts ?? 0) >= ($this->max_retries ?? 3) &&
               $this->status === MessageStatus::is_failed;
    }

    /**
     * Get retry status summary
     */
    public function getRetryStatusSummary(): array
    {
        return [
            'can_retry' => $this->canRetry(),
            'is_ready_for_retry' => $this->isReadyForRetry(),
            'has_failed_permanently' => $this->hasFailedPermanently(),
            'delivery_attempts' => $this->delivery_attempts ?? 0,
            'max_retries' => $this->max_retries ?? 3,
            'next_retry_at' => $this->next_retry_at?->toISOString(),
            'last_error_message' => $this->last_error_message,
            'minutes_until_retry' => $this->next_retry_at ? now()->diffInMinutes($this->next_retry_at, false) : null,
        ];
    }

    /**
     * Reset retry state (for manual retry)
     */
    public function resetRetryState(): void
    {
        $this->delivery_attempts = 0;
        $this->last_attempt_at = null;
        $this->next_retry_at = null;
    }

    /**
     * Check if message is from ChatBot
     */
    public function isFromChatBot(): bool
    {
        return $this->is_from_chatbot === true;
    }

    /**
     * Check if message is from Campaign
     */
    public function isFromCampaign(): bool
    {
        return $this->is_from_campaign === true;
    }

    /**
     * Mark message as ChatBot message
     */
    public function markAsFromChatBot(): void
    {
        $this->is_from_chatbot = true;
        $this->is_from_campaign = false;
    }

    /**
     * Mark message as Campaign message
     */
    public function markAsFromCampaign(): void
    {
        $this->is_from_chatbot = false;
        $this->is_from_campaign = true;
        $this->last_error_message = null;
        $this->status = MessageStatus::is_draft;
        $this->is_fail = false;
    }
}
