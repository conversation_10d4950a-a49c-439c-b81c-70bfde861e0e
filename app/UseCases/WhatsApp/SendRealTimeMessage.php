<?php

namespace App\UseCases\WhatsApp;

use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Domains\Inventory\Client;
use App\Enums\MessageStatus;
use App\Factories\ChatBot\MessageFactory;
use App\Http\Requests\WhatsApp\SendRealTimeMessageRequest;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\MessageService;
use App\UseCases\ChatBot\PhoneNumber\Get as GetPhoneNumber;
use App\UseCases\ChatBot\Template\Get as GetTemplate;
use App\UseCases\Inventory\Client\Get as GetClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use Throwable;

class SendRealTimeMessage
{
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;
    private GetClient $getClient;
    private GetPhoneNumber $getPhoneNumber;
    private GetTemplate $getTemplate;

    public function __construct(
        MessageRepository $messageRepository,
        MessageFactory $messageFactory,
        GetClient $getClient,
        GetPhoneNumber $getPhoneNumber,
        GetTemplate $getTemplate
    ) {
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
        $this->getClient = $getClient;
        $this->getPhoneNumber = $getPhoneNumber;
        $this->getTemplate = $getTemplate;
    }

    /**
     * @throws Throwable
     * @throws GuzzleException
     * @throws BindingResolutionException
     */
    public function perform(SendRealTimeMessageRequest $request): array
    {
        DB::beginTransaction();

        try {
            // 1. Validar permissões
            $this->validatePermissions($request);

            $client = $this->getClient->perform($request->client_id);
            $phoneNumber = $this->getPhoneNumber->perform($request->phone_number_id);
            $template = $request->template_id ? $this->getTemplate->perform($request->template_id) : null;

            $message = $this->messageFactory->buildFromSendIndividualMessage($request, $client, $template);

            $savedMessage = $this->messageRepository->store($message);

            /** @var MessageService $messageService */
            $messageService = app()->makeWith(MessageService::class, [
                'phoneNumber' => $phoneNumber,
            ]);

            $whatsappResponse = $messageService->send($savedMessage);

            DB::commit();

            return [
                'message_id' => $savedMessage->id,
                'whatsapp_message_id' => $whatsappResponse['messages'][0]['id'] ?? null,
                'status' => 'sent',
                'meta_response' => $whatsappResponse
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function validatePermissions(SendRealTimeMessageRequest $request): void
    {
        $organizationId = auth()->user()->organization_id;

        // Validar que client pertence à organização
        $client = $this->getClient->perform($request->client_id);
        if (!$client || $client->organization_id !== $organizationId) {
            throw new \Exception("Client does not belong to your organization", 403);
        }

        // Validar que phone_number pertence à organização
        $phoneNumber = $this->getPhoneNumber->perform($request->phone_number_id);
        if (!$phoneNumber || $phoneNumber->organization_id !== $organizationId) {
            throw new \Exception("Phone number does not belong to your organization", 403);
        }

        // Validar template se fornecido
        if ($request->template_id) {
            $template = $this->getTemplate->perform($request->template_id);
            if (!$template || $template->organization_id !== $organizationId) {
                throw new \Exception("Template does not belong to your organization", 403);
            }
        }
    }


}
