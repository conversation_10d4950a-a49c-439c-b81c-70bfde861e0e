<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Repositories\ExchangedMessageRepository;
use Carbon\Carbon;

class SaveOutboundFromStatusUpdate
{
    private ExchangedMessageFactory $exchangedMessageFactory;
    private ExchangedMessageRepository $exchangedMessageRepository;

    public function __construct(
        ExchangedMessageFactory $exchangedMessageFactory,
        ExchangedMessageRepository $exchangedMessageRepository
    ) {
        $this->exchangedMessageFactory = $exchangedMessageFactory;
        $this->exchangedMessageRepository = $exchangedMessageRepository;
    }

    public function perform(
        array $statusData,
        Organization $organization,
        PhoneNumber $phoneNumber,
        Message $message
    ): array {
        try {
            // 1. Verificar se o status é "delivered"
            $status = $statusData['status'] ?? null;
            if ($status !== 'delivered') {
                return [
                    'success' => false,
                    'reason' => 'Status is not delivered',
                    'processed' => 0
                ];
            }

            // 2. Verificar se já existe uma ExchangedMessage para esta message
            $existingExchangedMessage = $this->exchangedMessageRepository->fetchByMessageId($message->id, $organization->id);
            if ($existingExchangedMessage) {
                return [
                    'success' => false,
                    'reason' => 'ExchangedMessage already exists for this message',
                    'processed' => 0,
                    'existing_exchanged_message_id' => $existingExchangedMessage->id
                ];
            }

            // 3. Extrair sent_at do timestamp do webhook
            $timestamp = $statusData['timestamp'] ?? null;
            $sentAt = $timestamp ? Carbon::createFromTimestamp($timestamp) : Carbon::now();

            // 4. Criar ExchangedMessage usando a factory
            $exchangedMessage = $this->exchangedMessageFactory->buildFromOutboundMessage(
                $message,
                $phoneNumber,
                $sentAt
            );

            // 5. Salvar no banco
            $savedExchangedMessage = $this->exchangedMessageRepository->store($exchangedMessage);

            return [
                'success' => true,
                'reason' => 'Outbound message processed and saved successfully',
                'processed' => 1,
                'exchanged_message_id' => $savedExchangedMessage->id,
                'message_id' => $message->id,
                'wam_id' => $statusData['id'] ?? null
            ];

        } catch (\Illuminate\Database\QueryException $e) {
            // Check if it's a duplicate key error (unique constraint violation)
            if ($e->getCode() === '23000' || str_contains($e->getMessage(), 'unique_exchanged_message_per_organization')) {
                // This is a duplicate entry - another webhook already created the ExchangedMessage
                // Fetch the existing one and return success (idempotent behavior)
                $existingExchangedMessage = $this->exchangedMessageRepository->fetchByMessageId($message->id, $organization->id);

                return [
                    'success' => true,
                    'reason' => 'ExchangedMessage already exists (created by concurrent webhook)',
                    'processed' => 0,
                    'existing_exchanged_message_id' => $existingExchangedMessage?->id,
                    'message_id' => $message->id,
                    'wam_id' => $statusData['id'] ?? null,
                    'duplicate_prevented' => true
                ];
            }

            // Re-throw if it's not a duplicate key error
            throw $e;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'reason' => 'Error processing outbound status update: ' . $e->getMessage(),
                'processed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
