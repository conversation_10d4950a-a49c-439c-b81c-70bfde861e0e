<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Domains\WhatsApp\ChangeValueMessage;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WebHookDataFactory;
use App\UseCases\ChatBot\ExchangedMessage\SaveExchangedMessagesFromWebhook;
use App\Helpers\DBLog;

class ProcessWebhookMessage
{
    private ChatBotService $chatBotService;
    private SaveExchangedMessagesFromWebhook $saveExchangedMessagesFromWebhook;
    private WebHookDataFactory $webHookDataFactory;

    public function __construct(
        ChatBotService $chatBotService,
        SaveExchangedMessagesFromWebhook $saveExchangedMessagesFromWebhook,
        WebHookDataFactory $webHookDataFactory
    ) {
        $this->chatBotService = $chatBotService;
        $this->saveExchangedMessagesFromWebhook = $saveExchangedMessagesFromWebhook;
        $this->webHookDataFactory = $webHookDataFactory;
    }

    /**
     * Process webhook message using ChatBot service
     *
     * @param ChangeValue $changeValue
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @param int|null $webhookLogId
     * @return array
     */
    public function perform(ChangeValue $changeValue, Organization $organization, PhoneNumber $phoneNumber, ?int $webhookLogId = null): array
    {
        try {
            DBLog::logInfo(
                "processing webhook message ", "ProcessWebhookMessage::perform", $organization->id, null,
                ['phoneNumber' => $phoneNumber->toArray(), 'webhook_log_id' => $webhookLogId, 'changeValue' => $changeValue->toArray()]
            );

            if (!$phoneNumber->shouldProcessChatBot()) {
                $info = [
                    'success' => true,
                    'type' => 'manual',
                    'chatbot_processed' => false,
                    'reason_skipped' => $phoneNumber->getSkipChatBotReason(),
                    'processed_count' => $exchangedMessagesResult['processed'] ?? 0,
                    'organization_id' => $organization->id,
                    'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                    'webhook_log_id' => $webhookLogId
                ];

                DBLog::logInfo(
                    "ChatBot processing skipped for phone number","ProcessWebhookMessage", $organization->id, null,  $info
                );
                return $info;
            }

            $results = [];
            $processedCount = 0;

            $incomingMessages = $changeValue->getIncomingMessages($phoneNumber);

            DBLog::logInfo(
                "Saving exchanged messages from webhook","ProcessWebhookMessage", $organization->id, null,  [
                    'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                    'webhook_log_id' => $webhookLogId,
                    'incoming_messages' => $incomingMessages
                ]
            );

            $exchangedMessagesResult = $this->saveExchangedMessagesFromWebhook->perform(
                $changeValue,
                $organization,
                $phoneNumber,
                $incomingMessages,
                $webhookLogId
            );

            foreach ($incomingMessages as $messageData) {
                $message = new ChangeValueMessage($messageData);

                $webhookData = $this->webHookDataFactory->buildFromChangeValue(
                    $message,
                    $changeValue,
                    $organization,
                    $phoneNumber
                );

                $result = $this->chatBotService->processWebhook($webhookData, $phoneNumber);
                if ($result['success'] ?? false) {
                    $results[] = $result;
                    $processedCount++;
                }
            }

            return [
                'success' => true,
                'type' => 'chatbot',
                'chatbot_processed' => true,
                'processed_count' => $processedCount,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                'results' => $results,
                'exchanged_messages' => [
                    'processed' => $exchangedMessagesResult['processed'] ?? 0,
                    'total_messages' => $exchangedMessagesResult['total_messages'] ?? 0,
                    'success' => $exchangedMessagesResult['success'] ?? false
                ],
                'webhook_log_id' => $webhookLogId
            ];

        } catch (\Exception $e) {
            DBLog::logError(
                "Error processing webhook message: " . $e->getMessage(),
                "ProcessWebhookMessage",
                $organization->id,
                null,
                [
                    'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                    'webhook_log_id' => $webhookLogId,
                    'error' => $e->getMessage()
                ]
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => 'error',
                'chatbot_processed' => false,
                'processed_count' => 0,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                'webhook_log_id' => $webhookLogId
            ];
        }
    }
}
